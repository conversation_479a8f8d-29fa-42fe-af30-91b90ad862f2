// SPDX-License-Identifier: MIT
pragma solidity 0.8.24;

import {Test} from "forge-std/Test.sol";
import {console2} from "forge-std/console2.sol";
import "./TestContracts/DevTestSetup.sol";
import {IBorrowerOperations} from "src/Interfaces/IBorrowerOperations.sol";
import {ITroveManager} from "src/Interfaces/ITroveManager.sol";

/**
 * @title BorrowerOperationsInvariantFuzzer
 * @dev Invariant-based fuzzer for complex multi-step scenarios
 * Focus areas:
 * 1. System-wide invariants under stress
 * 2. Multi-step attack scenarios
 * 3. Batch operation consistency
 * 4. Interest rate arbitrage opportunities
 * 5. Liquidation and redemption edge cases
 */
contract BorrowerOperationsInvariantFuzzer is DevTestSetup {
    
    // System state tracking
    struct SystemSnapshot {
        uint256 totalColl;
        uint256 totalDebt;
        uint256 totalTroves;
        uint256 boldSupply;
        uint256 gasPoolBalance;
        uint256 timestamp;
    }
    
    SystemSnapshot internal initialSnapshot;
    SystemSnapshot internal currentSnapshot;
    
    // Trove tracking
    uint256[] internal allTroves;
    mapping(uint256 => address) internal troveOwners;
    mapping(address => uint256[]) internal userTroves;
    
    // Batch tracking
    address[] internal batchManagers;
    mapping(address => uint256[]) internal batchTroves;
    
    // Events for invariant violations
    event InvariantViolation(string invariant, string description, uint256 expected, uint256 actual);
    event SystemStateChange(string operation, uint256 collChange, uint256 debtChange);
    event AttackScenario(string scenario, address attacker, uint256 profit);
    
    function setUp() public override {
        super.setUp();
        priceFeed.setPrice(2000e18);
        _takeSystemSnapshot();
        initialSnapshot = currentSnapshot;
    }
    
    /// @dev Invariant: Total BOLD supply should equal total system debt
    function invariant_BoldSupplyEqualsSystemDebt() public {
        _takeSystemSnapshot();
        
        uint256 boldSupply = boldToken.totalSupply();
        uint256 systemDebt = troveManager.getEntireBranchDebt();
        
        // Allow small discrepancy due to pending interest
        uint256 tolerance = systemDebt / 1e6; // 0.0001% tolerance
        
        if (boldSupply > systemDebt + tolerance || systemDebt > boldSupply + tolerance) {
            emit InvariantViolation(
                "BOLD supply != system debt",
                "Total BOLD supply should equal total system debt",
                systemDebt,
                boldSupply
            );
        }
    }
    
    /// @dev Invariant: System collateral should equal sum of all trove collaterals
    function invariant_SystemCollateralConsistency() public {
        _takeSystemSnapshot();
        
        uint256 systemColl = troveManager.getEntireBranchColl();
        uint256 sumTroveColl = 0;
        
        uint256 troveCount = troveManager.getTroveIdsCount();
        for (uint256 i = 0; i < troveCount; i++) {
            uint256 troveId = troveManager.getTroveFromTroveIdsArray(i);
            sumTroveColl += troveManager.getTroveEntireColl(troveId);
        }
        
        // Account for gas compensation
        uint256 gasCompensation = troveCount * ETH_GAS_COMPENSATION;
        uint256 expectedSystemColl = sumTroveColl + gasCompensation;
        
        if (systemColl != expectedSystemColl) {
            emit InvariantViolation(
                "System collateral inconsistency",
                "System collateral should equal sum of trove collaterals plus gas compensation",
                expectedSystemColl,
                systemColl
            );
        }
    }
    
    /// @dev Invariant: All active troves should have ICR >= MCR
    function invariant_ActiveTrovesAboveMCR() public {
        uint256 price = priceFeed.getPrice();
        uint256 troveCount = troveManager.getTroveIdsCount();
        
        for (uint256 i = 0; i < troveCount; i++) {
            uint256 troveId = troveManager.getTroveFromTroveIdsArray(i);
            ITroveManager.Status status = troveManager.getTroveStatus(troveId);
            
            if (status == ITroveManager.Status.active) {
                uint256 icr = troveManager.getCurrentICR(troveId, price);
                
                // Check if trove is in batch (different MCR requirement)
                address batchManager = borrowerOperations.interestBatchManagerOf(troveId);
                uint256 requiredMCR = (batchManager != address(0)) ? MCR + BCR : MCR;
                
                if (icr < requiredMCR) {
                    emit InvariantViolation(
                        "Active trove below MCR",
                        "All active troves should have ICR >= MCR (+ BCR if in batch)",
                        requiredMCR,
                        icr
                    );
                }
            }
        }
    }
    
    /// @dev Invariant: SortedTroves should be properly ordered by interest rate
    function invariant_SortedTrovesOrdering() public {
        uint256 prev = sortedTroves.getFirst();
        if (prev == 0) return; // Empty list
        
        uint256 prevRate = troveManager.getTroveAnnualInterestRate(prev);
        uint256 curr = sortedTroves.getNext(prev);
        
        while (curr != 0) {
            uint256 currRate = troveManager.getTroveAnnualInterestRate(curr);
            
            if (currRate > prevRate) {
                emit InvariantViolation(
                    "SortedTroves ordering violation",
                    "Troves should be ordered by descending interest rate",
                    prevRate,
                    currRate
                );
            }
            
            prev = curr;
            prevRate = currRate;
            curr = sortedTroves.getNext(curr);
        }
    }
    
    /// @dev Test multi-step arbitrage attack scenario
    function testFuzz_InterestRateArbitrage(
        uint256 lowRate,
        uint256 highRate,
        uint256 collAmount,
        uint256 debtAmount,
        uint256 priceFluctuation
    ) public {
        lowRate = bound(lowRate, MIN_ANNUAL_INTEREST_RATE, MAX_ANNUAL_INTEREST_RATE / 2);
        highRate = bound(highRate, lowRate + 1e16, MAX_ANNUAL_INTEREST_RATE);
        collAmount = bound(collAmount, 10 ether, 1000 ether);
        debtAmount = bound(debtAmount, MIN_DEBT, 100000e18);
        priceFluctuation = bound(priceFluctuation, 80, 120); // 80-120% of base price
        
        uint256 basePrice = 2000e18;
        priceFeed.setPrice(basePrice);
        
        // Ensure valid ICR
        vm.assume(collAmount >= (debtAmount * MCR) / basePrice);
        
        // Step 1: Attacker opens trove with low interest rate
        uint256 attackerTrove = _openTrove(B, collAmount, debtAmount, lowRate);
        uint256 initialDebt = troveManager.getTroveEntireDebt(attackerTrove);
        
        emit SystemStateChange("Attacker trove opened", collAmount, initialDebt);
        
        // Step 2: Wait for interest to accrue
        vm.warp(block.timestamp + 30 days);
        
        // Step 3: Change price to create arbitrage opportunity
        uint256 newPrice = basePrice * priceFluctuation / 100;
        priceFeed.setPrice(newPrice);
        
        // Step 4: Attempt to exploit interest rate differences
        vm.startPrank(B);
        
        // Try to adjust interest rate for profit
        try borrowerOperations.adjustTroveInterestRate(
            attackerTrove,
            highRate,
            0,
            0,
            type(uint256).max
        ) {
            uint256 finalDebt = troveManager.getTroveEntireDebt(attackerTrove);
            
            // Check if attacker gained unfair advantage
            if (finalDebt < initialDebt) {
                emit AttackScenario("Interest rate arbitrage", B, initialDebt - finalDebt);
            }
        } catch {
            // Rate change failed - expected in many cases
        }
        
        vm.stopPrank();
        
        _checkAllInvariants();
    }
    
    /// @dev Test batch manipulation attack scenario
    function testFuzz_BatchManipulationAttack(
        uint128 managementFee,
        uint256 numTroves,
        uint256 collPerTrove,
        uint256 debtPerTrove
    ) public {
        managementFee = uint128(bound(managementFee, 0, MAX_ANNUAL_BATCH_MANAGEMENT_FEE));
        numTroves = bound(numTroves, 2, 10);
        collPerTrove = bound(collPerTrove, 10 ether, 100 ether);
        debtPerTrove = bound(debtPerTrove, MIN_DEBT, 50000e18);
        
        uint256 basePrice = 2000e18;
        priceFeed.setPrice(basePrice);
        vm.assume(collPerTrove >= (debtPerTrove * MCR) / basePrice);
        
        // Step 1: Attacker registers as batch manager
        vm.startPrank(B);
        borrowerOperations.registerBatchManager(
            uint128(MIN_ANNUAL_INTEREST_RATE),
            uint128(MAX_ANNUAL_INTEREST_RATE),
            uint128(MIN_ANNUAL_INTEREST_RATE),
            managementFee,
            uint128(MIN_INTEREST_RATE_CHANGE_PERIOD)
        );
        vm.stopPrank();
        
        batchManagers.push(B);
        
        // Step 2: Multiple users join the batch
        address[10] memory users = [A, C, D, E, F, G, H, I, J, K];
        
        for (uint256 i = 0; i < numTroves; i++) {
            uint256 troveId = _openTroveInBatch(
                users[i],
                collPerTrove,
                debtPerTrove,
                B
            );
            batchTroves[B].push(troveId);
        }
        
        emit SystemStateChange("Batch created", collPerTrove * numTroves, debtPerTrove * numTroves);
        
        // Step 3: Attacker attempts to manipulate batch for profit
        vm.warp(block.timestamp + 30 days);
        
        vm.startPrank(B);
        
        // Try to change batch interest rate for maximum profit
        try borrowerOperations.setBatchManagerAnnualInterestRate(
            uint128(MAX_ANNUAL_INTEREST_RATE),
            0,
            0,
            type(uint256).max
        ) {
            emit AttackScenario("Batch rate manipulation", B, managementFee);
        } catch {
            // Rate change failed
        }
        
        // Try to lower management fee (should only be allowed to lower)
        if (managementFee > 0) {
            try borrowerOperations.lowerBatchManagementFee(managementFee / 2) {
                emit SystemStateChange("Management fee lowered", 0, managementFee / 2);
            } catch {
                // Fee change failed
            }
        }
        
        vm.stopPrank();
        
        _checkAllInvariants();
    }
    
    /// @dev Test liquidation front-running scenario
    function testFuzz_LiquidationFrontRunning(
        uint256 victimColl,
        uint256 victimDebt,
        uint256 attackerColl,
        uint256 priceDropPercent
    ) public {
        victimColl = bound(victimColl, 10 ether, 100 ether);
        victimDebt = bound(victimDebt, MIN_DEBT, 80000e18);
        attackerColl = bound(attackerColl, 1 ether, 10 ether);
        priceDropPercent = bound(priceDropPercent, 10, 50); // 10-50% price drop
        
        uint256 basePrice = 2000e18;
        priceFeed.setPrice(basePrice);
        
        // Ensure valid ICRs initially
        vm.assume(victimColl >= (victimDebt * MCR) / basePrice);
        vm.assume(attackerColl >= (MIN_DEBT * MCR) / basePrice);
        
        // Step 1: Victim opens vulnerable trove
        uint256 victimTrove = _openTrove(C, victimColl, victimDebt, MIN_ANNUAL_INTEREST_RATE);
        
        // Step 2: Attacker opens trove
        uint256 attackerTrove = _openTrove(B, attackerColl, MIN_DEBT, MIN_ANNUAL_INTEREST_RATE);
        
        // Step 3: Price drops, making victim liquidatable
        uint256 newPrice = basePrice * (100 - priceDropPercent) / 100;
        priceFeed.setPrice(newPrice);
        
        uint256 victimICR = troveManager.getCurrentICR(victimTrove, newPrice);
        
        if (victimICR < MCR) {
            emit SystemStateChange("Price dropped, victim liquidatable", 0, victimICR);
            
            // Step 4: Attacker attempts front-running liquidation
            uint256 attackerCollBefore = troveManager.getTroveEntireColl(attackerTrove);
            
            vm.startPrank(B);
            try troveManager.liquidate(victimTrove) {
                uint256 attackerCollAfter = troveManager.getTroveEntireColl(attackerTrove);
                uint256 profit = attackerCollAfter > attackerCollBefore ? 
                    attackerCollAfter - attackerCollBefore : 0;
                
                if (profit > 0) {
                    emit AttackScenario("Liquidation front-running", B, profit);
                }
            } catch {
                // Liquidation failed
            }
            vm.stopPrank();
        }
        
        _checkAllInvariants();
    }
    
    /// @dev Helper function to open a trove
    function _openTrove(
        address owner,
        uint256 collAmount,
        uint256 debtAmount,
        uint256 interestRate
    ) internal returns (uint256) {
        deal(address(collToken), owner, collAmount);
        
        vm.startPrank(owner);
        collToken.approve(address(borrowerOperations), collAmount);
        
        uint256 troveId = borrowerOperations.openTrove(
            owner,
            0,
            collAmount,
            debtAmount,
            0,
            0,
            interestRate,
            type(uint256).max,
            address(0),
            address(0),
            address(0)
        );
        
        vm.stopPrank();
        
        allTroves.push(troveId);
        troveOwners[troveId] = owner;
        userTroves[owner].push(troveId);
        
        return troveId;
    }
    
    /// @dev Helper function to open a trove in a batch
    function _openTroveInBatch(
        address owner,
        uint256 collAmount,
        uint256 debtAmount,
        address batchManager
    ) internal returns (uint256) {
        deal(address(collToken), owner, collAmount);
        
        vm.startPrank(owner);
        collToken.approve(address(borrowerOperations), collAmount);
        
        uint256 troveId = borrowerOperations.openTroveAndJoinInterestBatchManager(
            owner,
            0,
            collAmount,
            debtAmount,
            0,
            0,
            batchManager,
            type(uint256).max,
            address(0),
            address(0),
            address(0)
        );
        
        vm.stopPrank();
        
        allTroves.push(troveId);
        troveOwners[troveId] = owner;
        userTroves[owner].push(troveId);
        
        return troveId;
    }
    
    /// @dev Take a snapshot of current system state
    function _takeSystemSnapshot() internal {
        currentSnapshot = SystemSnapshot({
            totalColl: troveManager.getEntireBranchColl(),
            totalDebt: troveManager.getEntireBranchDebt(),
            totalTroves: troveManager.getTroveIdsCount(),
            boldSupply: boldToken.totalSupply(),
            gasPoolBalance: weth.balanceOf(gasPoolAddress),
            timestamp: block.timestamp
        });
    }
    
    /// @dev Check all invariants
    function _checkAllInvariants() internal {
        invariant_BoldSupplyEqualsSystemDebt();
        invariant_SystemCollateralConsistency();
        invariant_ActiveTrovesAboveMCR();
        invariant_SortedTrovesOrdering();
    }
}
